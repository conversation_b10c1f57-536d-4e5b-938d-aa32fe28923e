source 'https://github.com/CocoaPods/Specs.git'
require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'



platform :ios, '12.4'
install! 'cocoapods', :deterministic_uuids => false

target 'ModrkClient' do
  config = use_native_modules!
  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways/Permission-LocationAlways.podspec"
  pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse/Permission-LocationWhenInUse.podspec"
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera/Permission-Camera.podspec"
  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications/Permission-Notifications.podspec"
  pod 'GoogleMaps'
  pod 'MobileVLCKit'
  pod 'Google-Maps-iOS-Utils'
  pod 'react-native-image-picker', :path => '../node_modules/react-native-image-picker'
  pod 'RNI18n', :path => '../node_modules/react-native-i18n'
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'AppsFlyerFramework'
  $RNFirebaseAsStaticFramework = true
  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # pod 'RNAudioRecorderPlayer', :path => '../node_modules/react-native-audio-recorder-player'

  pod 'rn-fetch-blob', :path => '../node_modules/rn-fetch-blob'

  pod 'react-native-vlc-media-player', :path => '../node_modules/react-native-vlc-media-player'


  pod 'RNSound', :path => '../node_modules/react-native-sound'

  target 'ModrkClientTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  use_flipper!('Flipper' => '0.201.0')

  post_install do |installer|
    
    react_native_post_install(installer)
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
    installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
  end
end
end
