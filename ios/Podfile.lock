PODS:
  - Apps<PERSON>lyerFramework (6.14.2):
    - AppsFlyerFramework/Main (= 6.14.2)
  - AppsFlyerFramework/Main (6.14.2)
  - boost (1.76.0)
  - BVLinearGradient (2.6.2):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.68.2)
  - FBReactNativeSpec (0.68.2):
    - RCT-<PERSON>olly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.2)
    - RCTTypeSafety (= 0.68.2)
    - React-Core (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - Firebase (9.6.0):
    - Firebase/Core (= 9.6.0)
  - Firebase/Core (9.6.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 9.6.0)
  - Firebase/CoreOnly (9.6.0):
    - FirebaseCore (= 9.6.0)
  - Firebase/Messaging (9.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 9.6.0)
  - FirebaseAnalytics (9.6.0):
    - FirebaseAnalytics/AdIdSupport (= 9.6.0)
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (9.6.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleAppMeasurement (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (9.6.0):
    - FirebaseCoreDiagnostics (~> 9.0)
    - FirebaseCoreInternal (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (9.6.0):
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCoreExtension (9.6.0):
    - FirebaseCore (~> 9.0)
  - FirebaseCoreInternal (9.6.0):
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
  - FirebaseInstallations (9.6.0):
    - FirebaseCore (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (9.6.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flipper (0.125.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (3.2.0)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.125.0):
    - FlipperKit/Core (= 0.125.0)
  - FlipperKit/Core (0.125.0):
    - Flipper (~> 0.125.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.125.0):
    - Flipper (~> 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.125.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.125.0)
  - FlipperKit/FKPortForwarding (0.125.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.125.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.1.0):
    - Google-Maps-iOS-Utils/Clustering (= 4.1.0)
    - Google-Maps-iOS-Utils/Geometry (= 4.1.0)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.1.0)
    - Google-Maps-iOS-Utils/Heatmap (= 4.1.0)
    - Google-Maps-iOS-Utils/QuadTree (= 4.1.0)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (4.1.0):
    - GoogleMaps
  - GoogleAppMeasurement (9.6.0):
    - GoogleAppMeasurement/AdIdSupport (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (9.6.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (9.6.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (5.2.0):
    - GoogleMaps/Maps (= 5.2.0)
  - GoogleMaps/Base (5.2.0)
  - GoogleMaps/Maps (5.2.0):
    - GoogleMaps/Base
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - libevent (2.1.12)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - MobileVLCKit (3.5.1)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - OpenSSL-Universal (1.1.1100)
  - Permission-Camera (3.6.1):
    - RNPermissions
  - Permission-LocationAlways (3.6.1):
    - RNPermissions
  - Permission-LocationWhenInUse (3.6.1):
    - RNPermissions
  - Permission-Notifications (3.6.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.68.2)
  - RCTTypeSafety (0.68.2):
    - FBLazyVector (= 0.68.2)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.2)
    - React-Core (= 0.68.2)
  - React (0.68.2):
    - React-Core (= 0.68.2)
    - React-Core/DevSupport (= 0.68.2)
    - React-Core/RCTWebSocket (= 0.68.2)
    - React-RCTActionSheet (= 0.68.2)
    - React-RCTAnimation (= 0.68.2)
    - React-RCTBlob (= 0.68.2)
    - React-RCTImage (= 0.68.2)
    - React-RCTLinking (= 0.68.2)
    - React-RCTNetwork (= 0.68.2)
    - React-RCTSettings (= 0.68.2)
    - React-RCTText (= 0.68.2)
    - React-RCTVibration (= 0.68.2)
  - React-callinvoker (0.68.2)
  - React-Codegen (0.68.2):
    - FBReactNativeSpec (= 0.68.2)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.2)
    - RCTTypeSafety (= 0.68.2)
    - React-Core (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-Core (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/Default (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/DevSupport (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.2)
    - React-Core/RCTWebSocket (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-jsinspector (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTWebSocket (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-CoreModules (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/CoreModulesHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-RCTImage (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-cxxreact (0.68.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsinspector (= 0.68.2)
    - React-logger (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - React-runtimeexecutor (= 0.68.2)
  - React-jsi (0.68.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.68.2)
  - React-jsi/Default (0.68.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.68.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-perflogger (= 0.68.2)
  - React-jsinspector (0.68.2)
  - React-logger (0.68.2):
    - glog
  - react-native-appsflyer (6.14.2-rc1):
    - AppsFlyerFramework (= 6.14.2)
    - React
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-document-picker (8.1.1):
    - React-Core
  - react-native-geolocation-service (5.3.0):
    - React
  - react-native-image-picker (4.8.5):
    - React-Core
  - react-native-image-resizer (1.4.5):
    - React-Core
  - react-native-maps (1.2.0):
    - React-Core
  - react-native-netinfo (9.3.0):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-restart (0.0.24):
    - React-Core
  - react-native-safe-area-context (4.3.1):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React
    - ReactCommon/turbomodule/core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-vlc-media-player (1.0.38):
    - MobileVLCKit (= 3.5.1)
    - React
    - TVVLCKit (= 3.5.1)
  - react-native-webview (11.23.0):
    - React-Core
  - React-perflogger (0.68.2)
  - React-RCTActionSheet (0.68.2):
    - React-Core/RCTActionSheetHeaders (= 0.68.2)
  - React-RCTAnimation (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTAnimationHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTBlob (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTBlobHeaders (= 0.68.2)
    - React-Core/RCTWebSocket (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-RCTNetwork (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTImage (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTImageHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-RCTNetwork (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTLinking (0.68.2):
    - React-Codegen (= 0.68.2)
    - React-Core/RCTLinkingHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTNetwork (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTNetworkHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTSettings (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTSettingsHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTText (0.68.2):
    - React-Core/RCTTextHeaders (= 0.68.2)
  - React-RCTVibration (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTVibrationHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-runtimeexecutor (0.68.2):
    - React-jsi (= 0.68.2)
  - ReactCommon/turbomodule/core (0.68.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.68.2)
    - React-Core (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-logger (= 0.68.2)
    - React-perflogger (= 0.68.2)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNAudioRecorderPlayer (3.5.1):
    - React-Core
  - RNCAsyncStorage (1.17.9):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.2.7):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDateTimePicker (3.5.2):
    - React-Core
  - RNDeviceInfo (10.3.0):
    - React-Core
  - RNFastImage (8.5.11):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (15.7.0):
    - Firebase/CoreOnly (= 9.6.0)
    - React-Core
  - RNFBMessaging (15.7.0):
    - Firebase/Messaging (= 9.6.0)
    - FirebaseCoreExtension (= 9.6.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNI18n (2.0.15):
    - React
  - RNPermissions (3.6.1):
    - React-Core
  - RNScreens (3.15.0):
    - React-Core
    - React-RCTImage
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNSVG (13.0.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - AppsFlyerFramework
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - Flipper (= 0.125.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= 3.2.0)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.125.0)
  - FlipperKit/Core (= 0.125.0)
  - FlipperKit/CppBridge (= 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.125.0)
  - FlipperKit/FBDefines (= 0.125.0)
  - FlipperKit/FKPortForwarding (= 0.125.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.125.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.125.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.125.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.125.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.125.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.125.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils
  - GoogleMaps
  - GoogleUtilities
  - MobileVLCKit
  - OpenSSL-Universal (= 1.1.1100)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera/Permission-Camera.podspec`)
  - Permission-LocationAlways (from `../node_modules/react-native-permissions/ios/LocationAlways/Permission-LocationAlways.podspec`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse/Permission-LocationWhenInUse.podspec`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications/Permission-Notifications.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-image-resizer (from `../node_modules/react-native-image-resizer`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-vlc-media-player (from `../node_modules/react-native-vlc-media-player`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNAudioRecorderPlayer (from `../node_modules/react-native-audio-recorder-player`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNI18n (from `../node_modules/react-native-i18n`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSound (from `../node_modules/react-native-sound`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - AppsFlyerFramework
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - libevent
    - libwebp
    - MobileVLCKit
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - YogaKit
  trunk:
    - GoogleMaps

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera/Permission-Camera.podspec"
  Permission-LocationAlways:
    :path: "../node_modules/react-native-permissions/ios/LocationAlways/Permission-LocationAlways.podspec"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse/Permission-LocationWhenInUse.podspec"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications/Permission-Notifications.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-image-resizer:
    :path: "../node_modules/react-native-image-resizer"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-vlc-media-player:
    :path: "../node_modules/react-native-vlc-media-player"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNAudioRecorderPlayer:
    :path: "../node_modules/react-native-audio-recorder-player"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNI18n:
    :path: "../node_modules/react-native-i18n"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSound:
    :path: "../node_modules/react-native-sound"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppsFlyerFramework: b3de9a49c6af8a8e38c44603e468b5e207f22466
  boost: a7c83b31436843459a1961bfd74b96033dc77234
  BVLinearGradient: 34a999fda29036898a09c6a6b728b0b4189e1a44
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  FBLazyVector: a7a655862f6b09625d11c772296b01cd5164b648
  FBReactNativeSpec: 81ce99032d5b586fddd6a38d450f8595f7e04be4
  Firebase: 5ae8b7cf8efce559a653aef0ad95bab3f427c351
  FirebaseAnalytics: 89ad762c6c3852a685794174757e2c60a36b6a82
  FirebaseCore: 2082fffcd855f95f883c0a1641133eb9bbe76d40
  FirebaseCoreDiagnostics: 99a495094b10a57eeb3ae8efa1665700ad0bdaa6
  FirebaseCoreExtension: e83465d1236b166d1d445bbf0e82b65acb30b73b
  FirebaseCoreInternal: bca76517fe1ed381e989f5e7d8abb0da8d85bed3
  FirebaseInstallations: 0a115432c4e223c5ab20b0dbbe4cbefa793a0e8e
  FirebaseMessaging: a4d7910e4af663c9cbfc1071c5bef34651690949
  Flipper: 26fc4b7382499f1281eb8cb921e5c3ad6de91fe0
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 3d3d04a078d4f3a1b6c6916587f159dc11f232c4
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 87bc98ff48de90cb5b0b5114ed3da79d85ee2dd4
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cbdee19bdd4e7f05472a66ce290f1b729ba3cb86
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 476ee3e89abb49e07f822b48323c51c57124b572
  Google-Maps-iOS-Utils: 3343332b18dfd5be8f1f44edd7d481ace3da4d9a
  GoogleAppMeasurement: 6de2b1a69e4326eb82ee05d138f6a5cb7311bcb1
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 025272d5876d3b32604e5c080dc25eaf68764693
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  MobileVLCKit: 144d5f565512d1147d63b0fa1379231b3fd66535
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  Permission-Camera: 4c0b610b46391c85d2d50f1446d98b4103b14b5a
  Permission-LocationAlways: cf23adac902686cd4f3f11826ea1ede821e2fcfd
  Permission-LocationWhenInUse: 667dfff996a63375d7f2ba3522ae841aa1f7aaf3
  Permission-Notifications: 49cae9775ac1e37c0e6cc28adca1d703527f1875
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 4d8508a426467c48885f1151029bc15fa5d7b3b8
  RCTRequired: 3e917ea5377751094f38145fdece525aa90545a0
  RCTTypeSafety: c43c072a4bd60feb49a9570b0517892b4305c45e
  React: 176dd882de001854ced260fad41bb68a31aa4bd0
  React-callinvoker: c2864d1818d6e64928d2faf774a3800dfc38fe1f
  React-Codegen: 98b6f97f0a7abf7d67e4ce435c77c05b7a95cf05
  React-Core: fdaa2916b1c893f39f02cff0476d1fb0cab1e352
  React-CoreModules: fd8705b80699ec36c2cdd635c2ce9d874b9cfdfc
  React-cxxreact: 1832d971f7b0cb2c7b943dc0ec962762c90c906e
  React-jsi: 72af715135abe8c3f0dcf3b2548b71d048b69a7e
  React-jsiexecutor: b7b553412f2ec768fe6c8f27cd6bafdb9d8719e6
  React-jsinspector: c5989c77cb89ae6a69561095a61cce56a44ae8e8
  React-logger: a0833912d93b36b791b7a521672d8ee89107aff1
  react-native-appsflyer: 370a3595c9492e4760096a2aaec044a15c25541d
  react-native-background-timer: 117fffdc9b0d6f144444bb49029f94275a45fdb5
  react-native-camera: 210a872c84d3aa0982a9223047ef9e743ed694fa
  react-native-document-picker: 3c074fa194fddbcd46867c3a1c44b9331bd9dcbc
  react-native-geolocation-service: f32ef48fa665bd710207a3ac39bc127cac9cb993
  react-native-image-picker: 24a36044140202085113ce99b57bf52c62dc339e
  react-native-image-resizer: 506412a2bdd70dde64a61e13505ce10f61a04369
  react-native-maps: 3c7f8dcd7ed97cd9979205a2da93e249149e5d9b
  react-native-netinfo: f473620bc118d6f44ce65669dd4810a7cfb36a7d
  react-native-pager-view: fc311e95f8ba2b710deae6cb0a21f2102fb49d99
  react-native-restart: 8af4579c94638f38bd9074ec477ebf087de87dc5
  react-native-safe-area-context: d768fdafd416b4c0bdc7bef4240c9ee53909baf3
  react-native-video: 79a186e378d327b6c08bc3982c17ef455c6bd75c
  react-native-vlc-media-player: 0f9860bded57cd6764ac55b5c0e97347069cb40a
  react-native-webview: c09ea739796b91286ae1f581d2db536b48c33911
  React-perflogger: a18b4f0bd933b8b24ecf9f3c54f9bf65180f3fe6
  React-RCTActionSheet: 547fe42fdb4b6089598d79f8e1d855d7c23e2162
  React-RCTAnimation: bc9440a1c37b06ae9ebbb532d244f607805c6034
  React-RCTBlob: a1295c8e183756d7ef30ba6e8f8144dfe8a19215
  React-RCTImage: a30d1ee09b1334067fbb6f30789aae2d7ac150c9
  React-RCTLinking: ffc6d5b88d1cb9aca13c54c2ec6507fbf07f2ac4
  React-RCTNetwork: f807a2facab6cf5cf36d592e634611de9cf12d81
  React-RCTSettings: 861806819226ed8332e6a8f90df2951a34bb3e7f
  React-RCTText: f3fb464cc41a50fc7a1aba4deeb76a9ad8282cb9
  React-RCTVibration: 79040b92bfa9c3c2d2cb4f57e981164ec7ab9374
  React-runtimeexecutor: b960b687d2dfef0d3761fbb187e01812ebab8b23
  ReactCommon: 095366164a276d91ea704ce53cb03825c487a3f2
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNAudioRecorderPlayer: b305a40d789b3a6c70802704eaba77c682bfe7a1
  RNCAsyncStorage: 7f9c2d854193313a97c9cc6673f66d2d59d7be81
  RNCClipboard: 5e299c6df8e0c98f3d7416b86ae563d3a9f768a3
  RNCMaskedView: ad49d114ee1faa6e4116c310276073b61a0b3dd2
  RNCPushNotificationIOS: 089da3b657e1e3d464f38195fd2e3069608ef5af
  RNDateTimePicker: c9911be59b1f8670b9f244b85af3a7c295e175ed
  RNDeviceInfo: e5dbe9fc01590f4bc5b9dda77e39ff6a39859224
  RNFastImage: 1f2cab428712a4baaf78d6169eaec7f622556dd7
  RNFBApp: ad6ad2dd37ccf10e737ec8688208761026331d64
  RNFBMessaging: f75d2d54bbf6ddc29304e2f45152bc397a8b5b3f
  RNGestureHandler: a479ebd5ed4221a810967000735517df0d2db211
  RNI18n: e2f7e76389fcc6e84f2c8733ea89b92502351fd8
  RNPermissions: 79d0c889562fd792329821bea3155fe6eb54d359
  RNScreens: 4a1af06327774490d97342c00aee0c2bafb497b7
  RNSound: 1081cf2576b404ca804daf4934bb644cb506ff98
  RNSVG: 42a0c731b11179ebbd27a3eeeafa7201ebb476ff
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 99652481fcd320aefa4a7ef90095b95acd181952
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 5b03a824bebb1d993a64b325523fed2e80f61d2e

COCOAPODS: 1.16.2
